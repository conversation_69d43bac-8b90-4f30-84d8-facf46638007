#!/usr/bin/env python3
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import os
import glob
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt

class SimpleAutoEncoder(nn.Module):
    """改进的自编码器，与训练脚本保持一致"""
    def __init__(self):
        super(SimpleAutoEncoder, self).__init__()
        
        # 编码器 - 逐步压缩
        self.encoder = nn.Sequential(
            # 256 -> 128
            nn.Conv2d(3, 64, 4, stride=2, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            
            # 128 -> 64
            nn.Conv2d(64, 128, 4, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            # 64 -> 32
            nn.Conv2d(128, 256, 4, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            # 32 -> 16
            nn.Conv2d(256, 512, 4, stride=2, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # 解码器 - 逐步重建
        self.decoder = nn.Sequential(
            # 16 -> 32
            nn.ConvTranspose2d(512, 256, 4, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            # 32 -> 64
            nn.ConvTranspose2d(256, 128, 4, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            # 64 -> 128
            nn.ConvTranspose2d(128, 64, 4, stride=2, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            
            # 128 -> 256
            nn.ConvTranspose2d(64, 3, 4, stride=2, padding=1),
            nn.Sigmoid()  # 输出[0,1]
        )
    
    def forward(self, x):
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded

def load_and_preprocess_image(image_path, size=(256, 256)):
    """加载和预处理图像"""
    transform = transforms.Compose([
        transforms.Resize(size),
        transforms.ToTensor(),
    ])
    
    image = Image.open(image_path).convert('RGB')
    return transform(image).unsqueeze(0)

def calculate_mse(img1, img2):
    """计算MSE"""
    if torch.is_tensor(img1):
        img1 = img1.cpu().numpy()
    if torch.is_tensor(img2):
        img2 = img2.cpu().numpy()
    
    img1 = np.clip(img1, 0, 1)
    img2 = np.clip(img2, 0, 1)
    
    return np.mean((img1 - img2) ** 2)

def extract_reconstruction_features(model, image_paths, device='cuda'):
    """提取重建特征"""
    model.eval()
    features = []
    
    with torch.no_grad():
        for img_path in image_paths:
            try:
                # 加载图像
                original = load_and_preprocess_image(img_path).to(device)
                
                # 重建
                reconstructed = model(original)
                
                # 计算MSE
                mse = calculate_mse(original[0], reconstructed[0])
                features.append(mse)
                
            except Exception as e:
                print(f"处理 {img_path} 时出错: {e}")
                features.append(0.0)  # 默认值
    
    return np.array(features)

def find_optimal_threshold(real_features, fake_features):
    """寻找最优阈值"""
    all_features = np.concatenate([real_features, fake_features])
    all_labels = np.concatenate([np.zeros(len(real_features)), np.ones(len(fake_features))])
    
    # 尝试不同的阈值
    thresholds = np.linspace(all_features.min(), all_features.max(), 100)
    best_threshold = 0
    best_f1 = 0
    
    results = []
    
    for threshold in thresholds:
        # 预测：MSE > threshold 为翻拍图
        predictions = (all_features > threshold).astype(int)
        
        # 计算指标
        accuracy = accuracy_score(all_labels, predictions)
        precision = precision_score(all_labels, predictions, zero_division=0)
        recall = recall_score(all_labels, predictions, zero_division=0)
        f1 = f1_score(all_labels, predictions, zero_division=0)
        
        results.append({
            'threshold': threshold,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        })
        
        if f1 > best_f1:
            best_f1 = f1
            best_threshold = threshold
    
    return best_threshold, results

def evaluate_classifier(real_features, fake_features, threshold):
    """评估分类器性能"""
    all_features = np.concatenate([real_features, fake_features])
    all_labels = np.concatenate([np.zeros(len(real_features)), np.ones(len(fake_features))])
    
    # 预测
    predictions = (all_features > threshold).astype(int)
    
    # 计算指标
    accuracy = accuracy_score(all_labels, predictions)
    precision = precision_score(all_labels, predictions, zero_division=0)
    recall = recall_score(all_labels, predictions, zero_division=0)
    f1 = f1_score(all_labels, predictions, zero_division=0)
    
    # 混淆矩阵
    cm = confusion_matrix(all_labels, predictions)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'confusion_matrix': cm,
        'predictions': predictions,
        'true_labels': all_labels
    }

def plot_results(real_features, fake_features, threshold):
    """绘制结果图"""
    plt.figure(figsize=(12, 4))
    
    # 子图1：特征分布
    plt.subplot(1, 3, 1)
    plt.hist(real_features, bins=20, alpha=0.7, label='Real Images', color='blue')
    plt.hist(fake_features, bins=20, alpha=0.7, label='Fake Images', color='red')
    plt.axvline(threshold, color='green', linestyle='--', label=f'Threshold={threshold:.4f}')
    plt.xlabel('Reconstruction MSE')
    plt.ylabel('Count')
    plt.title('MSE Distribution')
    plt.legend()
    
    # 子图2：箱线图
    plt.subplot(1, 3, 2)
    plt.boxplot([real_features, fake_features], labels=['Real', 'Fake'])
    plt.axhline(threshold, color='green', linestyle='--', label=f'Threshold={threshold:.4f}')
    plt.ylabel('Reconstruction MSE')
    plt.title('MSE Box Plot')
    plt.legend()
    
    # 子图3：统计信息
    plt.subplot(1, 3, 3)
    stats_text = f"""
    Real Images:
    Mean: {real_features.mean():.4f}
    Std: {real_features.std():.4f}
    
    Fake Images:
    Mean: {fake_features.mean():.4f}
    Std: {fake_features.std():.4f}
    
    Threshold: {threshold:.4f}
    """
    plt.text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center')
    plt.axis('off')
    plt.title('Statistics')
    
    plt.tight_layout()
    plt.savefig('reconstruction_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型并加载训练好的权重
    model = SimpleAutoEncoder().to(device)
    
    # 尝试加载训练好的模型
    model_path = 'best_reconstruction_model.pth'
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"加载训练好的模型: {model_path}")
    else:
        print("未找到训练好的模型，使用随机初始化")
    
    # 获取图像路径
    real_dir = "/media/lzz/Expansion/train_(副本)/real"
    fake_dir = "/media/lzz/Expansion/train_(副本)/fake"
    
    real_images = []
    fake_images = []
    
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        real_images.extend(glob.glob(os.path.join(real_dir, ext)))
        real_images.extend(glob.glob(os.path.join(real_dir, ext.upper())))
        fake_images.extend(glob.glob(os.path.join(fake_dir, ext)))
        fake_images.extend(glob.glob(os.path.join(fake_dir, ext.upper())))
    
    # 限制数量以加快测试
    real_images = real_images[:50]
    fake_images = fake_images[:50]
    
    print(f"真实图像: {len(real_images)} 张")
    print(f"翻拍图像: {len(fake_images)} 张")
    
    if not real_images or not fake_images:
        print("未找到足够的测试图像")
        return
    
    # 提取特征
    print("\n提取真实图像特征...")
    real_features = extract_reconstruction_features(model, real_images, device)
    
    print("提取翻拍图像特征...")
    fake_features = extract_reconstruction_features(model, fake_images, device)
    
    # 寻找最优阈值
    print("\n寻找最优阈值...")
    best_threshold, threshold_results = find_optimal_threshold(real_features, fake_features)
    
    # 评估性能
    print(f"\n最优阈值: {best_threshold:.6f}")
    results = evaluate_classifier(real_features, fake_features, best_threshold)
    
    print(f"\n=== 分类结果 ===")
    print(f"准确率: {results['accuracy']:.4f}")
    print(f"精确率: {results['precision']:.4f}")
    print(f"召回率: {results['recall']:.4f}")
    print(f"F1分数: {results['f1']:.4f}")
    
    print(f"\n混淆矩阵:")
    print(f"真实图像正确分类: {results['confusion_matrix'][0,0]}")
    print(f"真实图像误分类: {results['confusion_matrix'][0,1]}")
    print(f"翻拍图像误分类: {results['confusion_matrix'][1,0]}")
    print(f"翻拍图像正确分类: {results['confusion_matrix'][1,1]}")
    
    # 绘制结果
    plot_results(real_features, fake_features, best_threshold)
    
    print(f"\n结果图已保存为 reconstruction_analysis.png")

if __name__ == "__main__":
    main()