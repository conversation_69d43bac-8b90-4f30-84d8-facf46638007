#!/usr/bin/env python3
"""
V4版本数据预处理模块
包含频率域变换、小波变换和多模态数据处理功能
"""

import numpy as np
import cv2
import torch
import torch.nn.functional as F
from PIL import Image
import pywt
from scipy.fft import fft2, fftshift
import warnings
warnings.filterwarnings('ignore')


class FrequencyDomainProcessor:
    """频率域数据处理器"""
    
    def __init__(self, use_magnitude=True, use_phase=True, log_scale=True):
        """
        Args:
            use_magnitude: 是否使用幅度谱
            use_phase: 是否使用相位谱  
            log_scale: 是否对幅度谱使用对数缩放
        """
        self.use_magnitude = use_magnitude
        self.use_phase = use_phase
        self.log_scale = log_scale
    
    def process_image(self, image):
        """
        将RGB图像转换为频率域特征
        Args:
            image: PIL Image或numpy array (H, W, 3)
        Returns:
            torch.Tensor: 频率域特征 (C, H, W)
        """
        if isinstance(image, Image.Image):
            image = np.array(image)
        
        # 确保是RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            # 转换为灰度图进行FFT（也可以分别对RGB通道处理）
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # FFT变换
        fft_result = fft2(gray)
        fft_shifted = fftshift(fft_result)
        
        # 提取幅度谱和相位谱
        magnitude = np.abs(fft_shifted)
        phase = np.angle(fft_shifted)
        
        channels = []
        
        if self.use_magnitude:
            if self.log_scale:
                # 对数缩放避免动态范围过大
                magnitude_log = np.log(magnitude + 1e-8)
                magnitude_normalized = (magnitude_log - magnitude_log.min()) / (magnitude_log.max() - magnitude_log.min() + 1e-8)
            else:
                magnitude_normalized = (magnitude - magnitude.min()) / (magnitude.max() - magnitude.min() + 1e-8)
            channels.append(magnitude_normalized)
        
        if self.use_phase:
            # 相位谱归一化到[0,1]
            phase_normalized = (phase + np.pi) / (2 * np.pi)
            channels.append(phase_normalized)
        
        # 如果只有一个通道，复制到3个通道以匹配RGB格式
        if len(channels) == 1:
            channels = [channels[0], channels[0], channels[0]]
        elif len(channels) == 2:
            # 幅度谱 + 相位谱 + 组合特征
            combined = 0.7 * channels[0] + 0.3 * channels[1]
            channels.append(combined)
        
        # 转换为tensor格式 (C, H, W)
        result = np.stack(channels, axis=0).astype(np.float32)
        return torch.from_numpy(result)


class WaveletDomainProcessor:
    """小波域数据处理器"""
    
    def __init__(self, wavelet='db4', levels=3, mode='symmetric'):
        """
        Args:
            wavelet: 小波基类型 ('haar', 'db4', 'db8', 'bior2.2', 'coif2')
            levels: 分解层数
            mode: 边界处理模式
        """
        self.wavelet = wavelet
        self.levels = levels
        self.mode = mode
    
    def process_image(self, image):
        """
        将RGB图像转换为小波域特征
        Args:
            image: PIL Image或numpy array (H, W, 3)
        Returns:
            torch.Tensor: 小波域特征 (C, H, W)
        """
        if isinstance(image, Image.Image):
            image = np.array(image)
        
        # 确保是RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            # 转换为灰度图进行小波变换
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # 多级小波分解
        coeffs = pywt.wavedec2(gray, self.wavelet, level=self.levels, mode=self.mode)
        
        # 重构特征图
        channels = []
        
        # 低频分量（近似系数）
        cA = coeffs[0]
        cA_normalized = (cA - cA.min()) / (cA.max() - cA.min() + 1e-8)
        
        # 调整大小到原图尺寸
        target_size = gray.shape
        cA_resized = cv2.resize(cA_normalized, (target_size[1], target_size[0]))
        channels.append(cA_resized)
        
        # 高频分量（细节系数）
        detail_combined = np.zeros_like(gray, dtype=np.float32)
        
        for level_coeffs in coeffs[1:]:
            cH, cV, cD = level_coeffs  # 水平、垂直、对角细节
            
            # 组合三个方向的细节系数
            detail_level = np.sqrt(cH**2 + cV**2 + cD**2)
            detail_level_normalized = (detail_level - detail_level.min()) / (detail_level.max() - detail_level.min() + 1e-8)
            
            # 调整大小并累加
            detail_resized = cv2.resize(detail_level_normalized, (target_size[1], target_size[0]))
            detail_combined += detail_resized
        
        detail_combined = (detail_combined - detail_combined.min()) / (detail_combined.max() - detail_combined.min() + 1e-8)
        channels.append(detail_combined)
        
        # 创建第三个通道：低频和高频的组合
        combined = 0.6 * channels[0] + 0.4 * channels[1]
        channels.append(combined)
        
        # 转换为tensor格式 (C, H, W)
        result = np.stack(channels, axis=0).astype(np.float32)
        return torch.from_numpy(result)


class MultiModalProcessor:
    """多模态数据处理器"""
    
    def __init__(self, use_original=True, use_frequency=True, use_wavelet=True):
        """
        Args:
            use_original: 是否使用原始RGB图像
            use_frequency: 是否使用频率域特征
            use_wavelet: 是否使用小波域特征
        """
        self.use_original = use_original
        self.use_frequency = use_frequency
        self.use_wavelet = use_wavelet
        
        self.freq_processor = FrequencyDomainProcessor()
        self.wavelet_processor = WaveletDomainProcessor()
    
    def process_image(self, image):
        """
        处理图像生成多模态特征
        Args:
            image: PIL Image
        Returns:
            dict: 包含不同模态特征的字典
        """
        features = {}
        
        if self.use_original:
            # 原始RGB图像
            if isinstance(image, Image.Image):
                image_array = np.array(image)
            else:
                image_array = image
            
            # 转换为tensor格式 (C, H, W)
            original_tensor = torch.from_numpy(image_array.transpose(2, 0, 1)).float() / 255.0
            features['original'] = original_tensor
        
        if self.use_frequency:
            # 频率域特征
            freq_features = self.freq_processor.process_image(image)
            features['frequency'] = freq_features
        
        if self.use_wavelet:
            # 小波域特征
            wavelet_features = self.wavelet_processor.process_image(image)
            features['wavelet'] = wavelet_features
        
        return features


def test_processors():
    """测试数据处理器"""
    # 创建测试图像
    test_image = Image.new('RGB', (224, 224), (128, 128, 128))
    
    print("Testing FrequencyDomainProcessor...")
    freq_processor = FrequencyDomainProcessor()
    freq_result = freq_processor.process_image(test_image)
    print(f"Frequency domain output shape: {freq_result.shape}")
    print(f"Frequency domain value range: [{freq_result.min():.3f}, {freq_result.max():.3f}]")
    
    print("\nTesting WaveletDomainProcessor...")
    wavelet_processor = WaveletDomainProcessor()
    wavelet_result = wavelet_processor.process_image(test_image)
    print(f"Wavelet domain output shape: {wavelet_result.shape}")
    print(f"Wavelet domain value range: [{wavelet_result.min():.3f}, {wavelet_result.max():.3f}]")
    
    print("\nTesting MultiModalProcessor...")
    multi_processor = MultiModalProcessor()
    multi_result = multi_processor.process_image(test_image)
    for key, value in multi_result.items():
        print(f"{key} shape: {value.shape}, range: [{value.min():.3f}, {value.max():.3f}]")


if __name__ == '__main__':
    test_processors()
