#!/usr/bin/env python3
"""
屏幕翻拍检测模型性能对比分析脚本
用于对比V1 seeded、V4a频率域、V4b小波域、V4c融合版本的性能
生成详细的对比报告和可视化图表
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import argparse
from datetime import datetime

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def load_training_history(history_file):
    """加载训练历史文件"""
    try:
        with open(history_file, 'r') as f:
            history = json.load(f)
        return history
    except Exception as e:
        print(f"Error loading {history_file}: {e}")
        return None

def extract_best_metrics(history):
    """从训练历史中提取最佳指标"""
    if not history or 'val' not in history:
        return None
    
    val_history = history['val']
    
    # 找到最佳AUC对应的epoch
    best_auc_idx = 0
    best_auc = 0
    for i, epoch_data in enumerate(val_history):
        if epoch_data.get('auc', 0) > best_auc:
            best_auc = epoch_data['auc']
            best_auc_idx = i
    
    best_metrics = val_history[best_auc_idx]
    
    # 提取关键指标
    metrics = {
        'accuracy': best_metrics.get('accuracy', 0),
        'auc': best_metrics.get('auc', 0),
        'loss': best_metrics.get('loss', float('inf')),
        'epoch': best_auc_idx + 1
    }
    
    # 提取分类报告
    if 'classification_report' in best_metrics:
        report = best_metrics['classification_report']
        metrics.update({
            'real_precision': report.get('Real', {}).get('precision', 0),
            'real_recall': report.get('Real', {}).get('recall', 0),
            'real_f1': report.get('Real', {}).get('f1-score', 0),
            'fake_precision': report.get('Fake', {}).get('precision', 0),
            'fake_recall': report.get('Fake', {}).get('recall', 0),
            'fake_f1': report.get('Fake', {}).get('f1-score', 0),
            'macro_f1': report.get('macro avg', {}).get('f1-score', 0),
            'weighted_f1': report.get('weighted avg', {}).get('f1-score', 0)
        })
    
    # 提取注意力权重（仅V4c融合版本）
    if 'attention_weights' in best_metrics:
        att_weights = best_metrics['attention_weights']
        metrics.update({
            'attention_original': att_weights.get('original', 0),
            'attention_frequency': att_weights.get('frequency', 0),
            'attention_wavelet': att_weights.get('wavelet', 0)
        })
    
    return metrics

def create_comparison_table(models_data):
    """创建对比表格"""
    df_data = []
    
    for model_name, metrics in models_data.items():
        if metrics is None:
            continue
        
        row = {
            '模型版本': model_name,
            '准确率 (%)': f"{metrics['accuracy']:.2f}",
            'AUC': f"{metrics['auc']:.4f}",
            '损失': f"{metrics['loss']:.4f}",
            '最佳Epoch': metrics['epoch'],
            'Real精确率': f"{metrics.get('real_precision', 0):.3f}",
            'Real召回率': f"{metrics.get('real_recall', 0):.3f}",
            'Real F1': f"{metrics.get('real_f1', 0):.3f}",
            'Fake精确率': f"{metrics.get('fake_precision', 0):.3f}",
            'Fake召回率': f"{metrics.get('fake_recall', 0):.3f}",
            'Fake F1': f"{metrics.get('fake_f1', 0):.3f}",
            '宏平均F1': f"{metrics.get('macro_f1', 0):.3f}",
            '加权平均F1': f"{metrics.get('weighted_f1', 0):.3f}"
        }
        
        # 添加注意力权重（仅V4c）
        if 'attention_original' in metrics:
            row.update({
                '原图注意力': f"{metrics['attention_original']:.3f}",
                '频率域注意力': f"{metrics['attention_frequency']:.3f}",
                '小波域注意力': f"{metrics['attention_wavelet']:.3f}"
            })
        
        df_data.append(row)
    
    return pd.DataFrame(df_data)

def plot_performance_comparison(models_data, save_dir):
    """绘制性能对比图表"""
    # 准备数据
    model_names = []
    accuracies = []
    aucs = []
    losses = []
    
    for model_name, metrics in models_data.items():
        if metrics is None:
            continue
        model_names.append(model_name)
        accuracies.append(metrics['accuracy'])
        aucs.append(metrics['auc'])
        losses.append(metrics['loss'])
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('屏幕翻拍检测模型性能对比', fontsize=16, fontweight='bold')
    
    # 准确率对比
    axes[0, 0].bar(model_names, accuracies, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    axes[0, 0].set_title('准确率对比 (%)')
    axes[0, 0].set_ylabel('准确率 (%)')
    for i, v in enumerate(accuracies):
        axes[0, 0].text(i, v + 0.5, f'{v:.2f}', ha='center', va='bottom')
    
    # AUC对比
    axes[0, 1].bar(model_names, aucs, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    axes[0, 1].set_title('AUC对比')
    axes[0, 1].set_ylabel('AUC')
    for i, v in enumerate(aucs):
        axes[0, 1].text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom')
    
    # 损失对比
    axes[1, 0].bar(model_names, losses, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    axes[1, 0].set_title('验证损失对比')
    axes[1, 0].set_ylabel('损失')
    for i, v in enumerate(losses):
        axes[1, 0].text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom')
    
    # F1分数对比
    f1_scores = []
    for model_name, metrics in models_data.items():
        if metrics is None:
            continue
        f1_scores.append(metrics.get('macro_f1', 0))
    
    axes[1, 1].bar(model_names, f1_scores, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    axes[1, 1].set_title('宏平均F1分数对比')
    axes[1, 1].set_ylabel('F1分数')
    for i, v in enumerate(f1_scores):
        axes[1, 1].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

def plot_attention_weights(models_data, save_dir):
    """绘制注意力权重图（仅V4c融合版本）"""
    v4c_data = models_data.get('V4c融合版本')
    if v4c_data is None or 'attention_original' not in v4c_data:
        print("V4c融合版本数据不可用，跳过注意力权重可视化")
        return
    
    # 注意力权重数据
    attention_weights = [
        v4c_data['attention_original'],
        v4c_data['attention_frequency'],
        v4c_data['attention_wavelet']
    ]
    labels = ['原图特征', '频率域特征', '小波域特征']
    colors = ['#ff9999', '#66b3ff', '#99ff99']
    
    # 创建饼图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 饼图
    ax1.pie(attention_weights, labels=labels, colors=colors, autopct='%1.3f', startangle=90)
    ax1.set_title('V4c融合版本注意力权重分布', fontweight='bold')
    
    # 柱状图
    ax2.bar(labels, attention_weights, color=colors)
    ax2.set_title('V4c融合版本注意力权重对比', fontweight='bold')
    ax2.set_ylabel('注意力权重')
    for i, v in enumerate(attention_weights):
        ax2.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'attention_weights.png'), dpi=300, bbox_inches='tight')
    plt.close()

def generate_analysis_report(models_data, comparison_df, save_dir):
    """生成分析报告"""
    report_file = os.path.join(save_dir, 'performance_analysis_report.md')
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 屏幕翻拍检测模型性能对比分析报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 1. 模型版本说明\n\n")
        f.write("- **V1 Seeded版本**: 基础版本，使用原始RGB图像训练\n")
        f.write("- **V4a频率域版本**: 使用FFT变换提取频率域特征（幅度谱+相位谱）\n")
        f.write("- **V4b小波域版本**: 使用小波变换提取多尺度特征（低频+高频分量）\n")
        f.write("- **V4c融合版本**: 融合原图、频率域和小波域特征，使用注意力机制\n\n")
        
        f.write("## 2. 性能对比表格\n\n")
        f.write(comparison_df.to_markdown(index=False))
        f.write("\n\n")
        
        f.write("## 3. 关键发现\n\n")
        
        # 找到最佳模型
        best_auc_model = None
        best_auc = 0
        best_acc_model = None
        best_acc = 0
        
        for model_name, metrics in models_data.items():
            if metrics is None:
                continue
            if metrics['auc'] > best_auc:
                best_auc = metrics['auc']
                best_auc_model = model_name
            if metrics['accuracy'] > best_acc:
                best_acc = metrics['accuracy']
                best_acc_model = model_name
        
        f.write(f"### 3.1 最佳性能模型\n")
        f.write(f"- **AUC最高**: {best_auc_model} (AUC: {best_auc:.4f})\n")
        f.write(f"- **准确率最高**: {best_acc_model} (准确率: {best_acc:.2f}%)\n\n")
        
        f.write("### 3.2 各版本特点分析\n\n")
        
        for model_name, metrics in models_data.items():
            if metrics is None:
                continue
            
            f.write(f"**{model_name}**:\n")
            f.write(f"- 准确率: {metrics['accuracy']:.2f}%\n")
            f.write(f"- AUC: {metrics['auc']:.4f}\n")
            f.write(f"- 宏平均F1: {metrics.get('macro_f1', 0):.3f}\n")
            
            if 'attention_original' in metrics:
                f.write(f"- 注意力权重分布: 原图({metrics['attention_original']:.3f}), ")
                f.write(f"频率域({metrics['attention_frequency']:.3f}), ")
                f.write(f"小波域({metrics['attention_wavelet']:.3f})\n")
            
            f.write("\n")
        
        f.write("## 4. 结论与建议\n\n")
        f.write("### 4.1 性能排序\n")
        
        # 按AUC排序
        sorted_models = sorted([(name, data) for name, data in models_data.items() if data is not None], 
                              key=lambda x: x[1]['auc'], reverse=True)
        
        for i, (model_name, metrics) in enumerate(sorted_models, 1):
            f.write(f"{i}. {model_name} (AUC: {metrics['auc']:.4f})\n")
        
        f.write("\n### 4.2 应用建议\n\n")
        f.write("- **追求最高准确率**: 选择准确率最高的模型\n")
        f.write("- **平衡性能与复杂度**: 考虑模型复杂度和推理速度\n")
        f.write("- **特定场景优化**: 根据数据特点选择合适的特征域\n")
        f.write("- **融合策略**: V4c融合版本展示了多模态融合的潜力\n\n")
        
        f.write("## 5. 技术细节\n\n")
        f.write("- **训练参数**: 所有版本使用相同的训练参数确保公平对比\n")
        f.write("- **随机种子**: 固定种子42确保结果可复现\n")
        f.write("- **数据增强**: 保持一致的数据增强策略\n")
        f.write("- **评估指标**: 使用准确率、AUC、F1分数等多维度评估\n")
    
    print(f"分析报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='Screen Recapture Detection Models Performance Comparison')
    parser.add_argument('--v1_history', type=str, help='V1 seeded版本训练历史文件路径')
    parser.add_argument('--v4a_history', type=str, help='V4a频率域版本训练历史文件路径')
    parser.add_argument('--v4b_history', type=str, help='V4b小波域版本训练历史文件路径')
    parser.add_argument('--v4c_history', type=str, help='V4c融合版本训练历史文件路径')
    parser.add_argument('--output_dir', type=str, default='./performance_analysis', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载各版本的训练历史
    models_data = {}
    
    if args.v1_history and os.path.exists(args.v1_history):
        history = load_training_history(args.v1_history)
        models_data['V1 Seeded版本'] = extract_best_metrics(history)
    
    if args.v4a_history and os.path.exists(args.v4a_history):
        history = load_training_history(args.v4a_history)
        models_data['V4a频率域版本'] = extract_best_metrics(history)
    
    if args.v4b_history and os.path.exists(args.v4b_history):
        history = load_training_history(args.v4b_history)
        models_data['V4b小波域版本'] = extract_best_metrics(history)
    
    if args.v4c_history and os.path.exists(args.v4c_history):
        history = load_training_history(args.v4c_history)
        models_data['V4c融合版本'] = extract_best_metrics(history)
    
    if not models_data:
        print("错误: 没有找到有效的训练历史文件")
        print("请确保至少提供一个有效的训练历史文件路径")
        return
    
    print(f"成功加载 {len(models_data)} 个模型的训练历史")
    
    # 创建对比表格
    comparison_df = create_comparison_table(models_data)
    comparison_df.to_csv(os.path.join(args.output_dir, 'performance_comparison.csv'), index=False)
    print(f"对比表格已保存到: {os.path.join(args.output_dir, 'performance_comparison.csv')}")
    
    # 绘制性能对比图表
    plot_performance_comparison(models_data, args.output_dir)
    print(f"性能对比图表已保存到: {os.path.join(args.output_dir, 'performance_comparison.png')}")
    
    # 绘制注意力权重图（如果有V4c数据）
    plot_attention_weights(models_data, args.output_dir)
    
    # 生成分析报告
    generate_analysis_report(models_data, comparison_df, args.output_dir)
    
    print("\n性能对比分析完成!")
    print(f"所有结果已保存到: {args.output_dir}")


if __name__ == '__main__':
    main()
