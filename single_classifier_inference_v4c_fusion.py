#!/usr/bin/env python3
"""
单分类器屏幕翻拍检测推理脚本 V4c - 多模态融合版本
- 基于V1 seeded版本的推理流程
- 同时处理原图、频率域和小波域特征
- 支持单张图像和批量推理
"""

import os
import torch
import torch.nn as nn
from torchvision import transforms
import numpy as np
from PIL import Image
import argparse
import json
import cv2
from tqdm import tqdm
import glob

from model.efficientnet import EfficientNet
from data_preprocessing_v4 import MultiModalProcessor


class AttentionFusion(nn.Module):
    """注意力融合模块"""
    
    def __init__(self, feature_dim):
        super().__init__()
        self.feature_dim = feature_dim
        
        # 注意力权重计算
        self.attention = nn.Sequential(
            nn.Linear(feature_dim * 3, feature_dim),
            nn.ReLU(inplace=True),
            nn.Linear(feature_dim, 3),
            nn.Softmax(dim=1)
        )
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(feature_dim * 2, feature_dim)
        )
    
    def forward(self, original_features, freq_features, wavelet_features):
        """
        Args:
            original_features: 原图特征 (B, feature_dim)
            freq_features: 频率域特征 (B, feature_dim)
            wavelet_features: 小波域特征 (B, feature_dim)
        Returns:
            fused_features: 融合后的特征 (B, feature_dim)
        """
        # 拼接所有特征
        concat_features = torch.cat([original_features, freq_features, wavelet_features], dim=1)
        
        # 计算注意力权重
        attention_weights = self.attention(concat_features)  # (B, 3)
        
        # 加权融合
        weighted_original = original_features * attention_weights[:, 0:1]
        weighted_freq = freq_features * attention_weights[:, 1:2]
        weighted_wavelet = wavelet_features * attention_weights[:, 2:3]
        
        # 特征融合
        fused_input = torch.cat([weighted_original, weighted_freq, weighted_wavelet], dim=1)
        fused_features = self.fusion(fused_input)
        
        return fused_features, attention_weights


class MultiModalScreenRecaptureDetector(nn.Module):
    """多模态单分类器屏幕翻拍检测模型"""
    
    def __init__(self, backbone='efficientnet-b4', num_classes=2, drop_rate=0.2):
        super().__init__()
        
        # 三个独立的主干网络用于不同模态
        self.original_backbone = EfficientNet.from_pretrained(
            backbone, advprop=True, num_classes=num_classes,
            dropout_rate=drop_rate, include_top=False
        )
        
        self.freq_backbone = EfficientNet.from_pretrained(
            backbone, advprop=True, num_classes=num_classes,
            dropout_rate=drop_rate, include_top=False
        )
        
        self.wavelet_backbone = EfficientNet.from_pretrained(
            backbone, advprop=True, num_classes=num_classes,
            dropout_rate=drop_rate, include_top=False
        )
        
        # 获取backbone输出特征维度
        backbone_features = self.original_backbone._bn1.num_features  # EfficientNet-B4: 1792
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 注意力融合模块
        self.attention_fusion = AttentionFusion(backbone_features)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(drop_rate),
            nn.Linear(backbone_features, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(256, num_classes)
        )
    
    def extract_backbone_features(self, x, backbone):
        """提取主干网络特征"""
        # Stem
        x = backbone._swish(backbone._bn0(backbone._conv_stem(x)))
        
        # Blocks
        for idx, block in enumerate(backbone._blocks):
            drop_connect_rate = backbone._global_params.drop_connect_rate
            if drop_connect_rate:
                drop_connect_rate *= float(idx) / len(backbone._blocks)
            x = block(x, drop_connect_rate=drop_connect_rate)
        
        # Head
        x = backbone._swish(backbone._bn1(backbone._conv_head(x)))
        
        return x
    
    def forward(self, original_x, freq_x, wavelet_x):
        """
        Args:
            original_x: 原图输入 (B, 3, H, W)
            freq_x: 频率域输入 (B, 3, H, W)
            wavelet_x: 小波域输入 (B, 3, H, W)
        """
        # 提取各模态特征
        original_features = self.extract_backbone_features(original_x, self.original_backbone)
        freq_features = self.extract_backbone_features(freq_x, self.freq_backbone)
        wavelet_features = self.extract_backbone_features(wavelet_x, self.wavelet_backbone)
        
        # 全局池化
        original_pooled = self.global_pool(original_features).view(original_features.size(0), -1)
        freq_pooled = self.global_pool(freq_features).view(freq_features.size(0), -1)
        wavelet_pooled = self.global_pool(wavelet_features).view(wavelet_features.size(0), -1)
        
        # 注意力融合
        fused_features, attention_weights = self.attention_fusion(
            original_pooled, freq_pooled, wavelet_pooled
        )
        
        # 分类
        output = self.classifier(fused_features)
        
        return output, attention_weights


def load_model(checkpoint_path, device):
    """加载训练好的模型"""
    print(f"Loading model from {checkpoint_path}")
    
    # 加载checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 创建模型
    model = MultiModalScreenRecaptureDetector(backbone='efficientnet-b4', num_classes=2)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f"Model loaded successfully!")
    print(f"Model type: {checkpoint.get('model_type', 'unknown')}")
    print(f"Best AUC: {checkpoint.get('best_auc', 'unknown')}")
    print(f"Training seed: {checkpoint.get('seed', 'unknown')}")
    
    return model


def get_transforms(input_size=224):
    """获取推理时的数据变换"""
    
    # 原图使用标准的ImageNet归一化
    original_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 频率域和小波域使用不同的归一化
    domain_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    def combined_transform(features_dict):
        result = {}
        for key, features in features_dict.items():
            if key == 'original':
                result[key] = original_transform(features)
            else:
                result[key] = domain_transform(features)
        return result
    
    return combined_transform


def preprocess_image(image_path, multimodal_processor, transform):
    """预处理单张图像"""
    try:
        # 加载图像
        image = Image.open(image_path).convert('RGB')
        
        # 生成多模态特征
        multimodal_features = multimodal_processor.process_image(image)
        
        # 应用变换
        if transform:
            multimodal_features = transform(multimodal_features)
        
        # 添加batch维度
        for key in multimodal_features:
            multimodal_features[key] = multimodal_features[key].unsqueeze(0)
        
        return multimodal_features
        
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return None


def predict_single_image(model, image_path, multimodal_processor, transform, device):
    """对单张图像进行预测"""
    # 预处理
    multimodal_features = preprocess_image(image_path, multimodal_processor, transform)
    if multimodal_features is None:
        return None
    
    # 移到设备
    original_x = multimodal_features['original'].to(device)
    freq_x = multimodal_features['frequency'].to(device)
    wavelet_x = multimodal_features['wavelet'].to(device)
    
    # 推理
    with torch.no_grad():
        outputs, attention_weights = model(original_x, freq_x, wavelet_x)
        probs = torch.softmax(outputs, dim=1)
        confidence = torch.max(probs, dim=1)[0].item()
        predicted_class = torch.argmax(outputs, dim=1).item()
    
    # 结果
    result = {
        'image_path': image_path,
        'predicted_class': predicted_class,
        'predicted_label': 'Fake' if predicted_class == 1 else 'Real',
        'confidence': confidence,
        'real_prob': probs[0][0].item(),
        'fake_prob': probs[0][1].item(),
        'attention_weights': {
            'original': attention_weights[0][0].item(),
            'frequency': attention_weights[0][1].item(),
            'wavelet': attention_weights[0][2].item()
        }
    }
    
    return result


def predict_batch(model, image_paths, multimodal_processor, transform, device, batch_size=8):
    """批量预测"""
    results = []

    for i in tqdm(range(0, len(image_paths), batch_size), desc="Processing batches"):
        batch_paths = image_paths[i:i+batch_size]
        batch_original = []
        batch_freq = []
        batch_wavelet = []
        valid_paths = []

        # 预处理batch
        for path in batch_paths:
            multimodal_features = preprocess_image(path, multimodal_processor, transform)
            if multimodal_features is not None:
                batch_original.append(multimodal_features['original'])
                batch_freq.append(multimodal_features['frequency'])
                batch_wavelet.append(multimodal_features['wavelet'])
                valid_paths.append(path)

        if not batch_original:
            continue

        # 合并为batch
        batch_original_input = torch.cat(batch_original, dim=0).to(device)
        batch_freq_input = torch.cat(batch_freq, dim=0).to(device)
        batch_wavelet_input = torch.cat(batch_wavelet, dim=0).to(device)

        # 推理
        with torch.no_grad():
            outputs, attention_weights = model(batch_original_input, batch_freq_input, batch_wavelet_input)
            probs = torch.softmax(outputs, dim=1)
            confidences = torch.max(probs, dim=1)[0]
            predicted_classes = torch.argmax(outputs, dim=1)

        # 收集结果
        for j, path in enumerate(valid_paths):
            result = {
                'image_path': path,
                'predicted_class': predicted_classes[j].item(),
                'predicted_label': 'Fake' if predicted_classes[j].item() == 1 else 'Real',
                'confidence': confidences[j].item(),
                'real_prob': probs[j][0].item(),
                'fake_prob': probs[j][1].item(),
                'attention_weights': {
                    'original': attention_weights[j][0].item(),
                    'frequency': attention_weights[j][1].item(),
                    'wavelet': attention_weights[j][2].item()
                }
            }
            results.append(result)

    return results


def main():
    parser = argparse.ArgumentParser(description='Single Classifier Screen Recapture Detector Inference V4c - Multi-Modal Fusion')
    parser.add_argument('--checkpoint', type=str, required=True, help='模型checkpoint路径')
    parser.add_argument('--input', type=str, required=True, help='输入图像路径或目录')
    parser.add_argument('--output', type=str, help='输出结果文件路径 (JSON格式)')
    parser.add_argument('--batch_size', type=int, default=8, help='批量推理的批次大小 (融合模型需要更多内存)')
    parser.add_argument('--input_size', type=int, default=224, help='输入图像尺寸')

    args = parser.parse_args()

    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # 加载模型
    model = load_model(args.checkpoint, device)

    # 初始化多模态处理器和变换
    multimodal_processor = MultiModalProcessor(
        use_original=True,
        use_frequency=True,
        use_wavelet=True
    )
    transform = get_transforms(args.input_size)

    # 确定输入类型
    if os.path.isfile(args.input):
        # 单张图像
        print(f"Processing single image: {args.input}")
        result = predict_single_image(model, args.input, multimodal_processor, transform, device)

        if result:
            print(f"\nPrediction Result:")
            print(f"Image: {result['image_path']}")
            print(f"Predicted: {result['predicted_label']}")
            print(f"Confidence: {result['confidence']:.4f}")
            print(f"Real Probability: {result['real_prob']:.4f}")
            print(f"Fake Probability: {result['fake_prob']:.4f}")
            print(f"Attention Weights:")
            print(f"  Original: {result['attention_weights']['original']:.3f}")
            print(f"  Frequency: {result['attention_weights']['frequency']:.3f}")
            print(f"  Wavelet: {result['attention_weights']['wavelet']:.3f}")

            results = [result]
        else:
            print("Failed to process the image.")
            return

    elif os.path.isdir(args.input):
        # 目录批量处理
        print(f"Processing directory: {args.input}")

        # 收集所有图像文件
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        image_paths = []
        for ext in image_extensions:
            image_paths.extend(glob.glob(os.path.join(args.input, '**', ext), recursive=True))

        print(f"Found {len(image_paths)} images")

        if not image_paths:
            print("No images found in the directory.")
            return

        # 批量预测
        results = predict_batch(model, image_paths, multimodal_processor, transform, device, args.batch_size)

        # 统计结果
        real_count = sum(1 for r in results if r['predicted_class'] == 0)
        fake_count = sum(1 for r in results if r['predicted_class'] == 1)

        # 统计平均注意力权重
        avg_attention = {
            'original': np.mean([r['attention_weights']['original'] for r in results]),
            'frequency': np.mean([r['attention_weights']['frequency'] for r in results]),
            'wavelet': np.mean([r['attention_weights']['wavelet'] for r in results])
        }

        print(f"\nBatch Processing Results:")
        print(f"Total processed: {len(results)}")
        print(f"Predicted Real: {real_count}")
        print(f"Predicted Fake: {fake_count}")
        print(f"Average Attention Weights:")
        print(f"  Original: {avg_attention['original']:.3f}")
        print(f"  Frequency: {avg_attention['frequency']:.3f}")
        print(f"  Wavelet: {avg_attention['wavelet']:.3f}")

    else:
        print(f"Invalid input path: {args.input}")
        return

    # 保存结果
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\nResults saved to: {args.output}")

    print("\n单分类器模型 V4c - 多模态融合版本 推理完成!")
    print("模型特点:")
    print("- 融合原图、频率域和小波域特征")
    print("- 注意力机制自动学习各模态重要性")
    print("- 更全面的特征表示能力")
    print("- 基于V1 seeded版本架构")


if __name__ == '__main__':
    main()
