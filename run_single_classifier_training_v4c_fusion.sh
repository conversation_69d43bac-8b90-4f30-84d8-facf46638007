#!/bin/bash

# 单分类器屏幕翻拍检测训练脚本 V4c - 多模态融合版本
# 基于V1 seeded版本，保持相同的训练参数以便对比

echo "开始训练单分类器屏幕翻拍检测模型 V4c - 多模态融合版本"
echo "模型特点:"
echo "1. 基于V1 seeded版本架构"
echo "2. 融合原图、频率域和小波域特征"
echo "3. 使用注意力机制进行特征融合"
echo "4. 三个独立的EfficientNet-B4 backbone"
echo "5. 保持与V1一致的训练参数"
echo "6. 随机种子42确保可复现性"
echo ""

# 训练参数 (与V1 seeded版本保持一致，但batch_size需要调小因为融合模型更大)
BATCH_SIZE=8  # 融合模型需要更多内存，可能需要调整
EPOCHS=30
LEARNING_RATE=1e-4
WEIGHT_DECAY=1e-4
INPUT_SIZE=224
BACKBONE="efficientnet-b4"
SEED=42

# 数据路径 (需要根据实际情况修改)
TRAIN_DIR="../data/train"  # 训练数据目录，包含real/和fake/子目录
VAL_DIR="../data/val"      # 验证数据目录，包含real/和fake/子目录

# 模型保存路径
SAVE_DIR="./single_classifier_checkpoints_v4c_fusion"

# 检查数据目录是否存在
if [ ! -d "$TRAIN_DIR" ]; then
    echo "错误: 训练数据目录不存在: $TRAIN_DIR"
    echo "请确保数据目录结构如下:"
    echo "  $TRAIN_DIR/"
    echo "    ├── real/     (真实拍摄图像)"
    echo "    └── fake/     (屏幕翻拍图像)"
    exit 1
fi

if [ ! -d "$VAL_DIR" ]; then
    echo "错误: 验证数据目录不存在: $VAL_DIR"
    echo "请确保数据目录结构如下:"
    echo "  $VAL_DIR/"
    echo "    ├── real/     (真实拍摄图像)"
    echo "    └── fake/     (屏幕翻拍图像)"
    exit 1
fi

# 创建保存目录
mkdir -p "$SAVE_DIR"

# 记录训练参数
echo "训练参数:"
echo "  批次大小: $BATCH_SIZE (融合模型需要更多内存)"
echo "  训练轮数: $EPOCHS"
echo "  学习率: $LEARNING_RATE"
echo "  权重衰减: $WEIGHT_DECAY"
echo "  输入尺寸: $INPUT_SIZE"
echo "  主干网络: $BACKBONE"
echo "  随机种子: $SEED"
echo "  训练数据: $TRAIN_DIR"
echo "  验证数据: $VAL_DIR"
echo "  保存目录: $SAVE_DIR"
echo ""

# 检查GPU内存
echo "检查GPU状态..."
nvidia-smi --query-gpu=memory.total,memory.used,memory.free --format=csv,noheader,nounits 2>/dev/null || echo "无法获取GPU信息，请确保CUDA可用"
echo ""

# 开始训练
echo "开始训练..."
python single_classifier_trainer_v4c_fusion.py \
    --train_dir "$TRAIN_DIR" \
    --val_dir "$VAL_DIR" \
    --batch_size $BATCH_SIZE \
    --epochs $EPOCHS \
    --lr $LEARNING_RATE \
    --weight_decay $WEIGHT_DECAY \
    --save_dir "$SAVE_DIR" \
    --input_size $INPUT_SIZE \
    --backbone "$BACKBONE" \
    --seed $SEED

# 检查训练是否成功
if [ $? -eq 0 ]; then
    echo ""
    echo "训练完成!"
    echo "模型文件保存在: $SAVE_DIR"
    echo "  - best_single_classifier_v4c_fusion.pth (最佳模型)"
    echo "  - latest_single_classifier_v4c_fusion.pth (最新模型)"
    echo "  - single_classifier_history_v4c_fusion.json (训练历史)"
    echo ""
    echo "V4c多模态融合版本特点:"
    echo "- 融合原图、频率域和小波域的互补信息"
    echo "- 注意力机制自动学习各模态的重要性"
    echo "- 更全面的特征表示能力"
    echo "- 保持与V1相同的训练策略"
    echo ""
    echo "推理使用方法:"
    echo "python single_classifier_inference_v4c_fusion.py \\"
    echo "    --checkpoint $SAVE_DIR/best_single_classifier_v4c_fusion.pth \\"
    echo "    --input /path/to/image_or_directory \\"
    echo "    --output results_v4c_fusion.json"
else
    echo ""
    echo "训练失败! 请检查错误信息。"
    echo "如果遇到内存不足错误，请尝试减小batch_size:"
    echo "  - 修改脚本中的BATCH_SIZE=4或更小"
    echo "  - 或者在命令行中手动运行并指定更小的batch_size"
    exit 1
fi
