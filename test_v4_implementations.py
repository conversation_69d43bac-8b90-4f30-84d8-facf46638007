#!/usr/bin/env python3
"""
V4版本实现测试脚本
测试所有V4版本的数据预处理、模型创建、训练和推理流程
确保实现的正确性
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import tempfile
import shutil

def test_data_preprocessing():
    """测试数据预处理模块"""
    print("=" * 50)
    print("测试数据预处理模块")
    print("=" * 50)
    
    try:
        from data_preprocessing_v4 import FrequencyDomainProcessor, WaveletDomainProcessor, MultiModalProcessor
        
        # 创建测试图像
        test_image = Image.new('RGB', (224, 224), color='red')
        
        # 测试频率域处理器
        print("1. 测试频率域处理器...")
        freq_processor = FrequencyDomainProcessor()
        freq_features = freq_processor.process_image(test_image)
        print(f"   频率域特征形状: {freq_features.shape}")
        assert freq_features.shape == (3, 224, 224), f"频率域特征形状错误: {freq_features.shape}"
        print("   ✓ 频率域处理器测试通过")
        
        # 测试小波域处理器
        print("2. 测试小波域处理器...")
        wavelet_processor = WaveletDomainProcessor()
        wavelet_features = wavelet_processor.process_image(test_image)
        print(f"   小波域特征形状: {wavelet_features.shape}")
        assert wavelet_features.shape == (3, 224, 224), f"小波域特征形状错误: {wavelet_features.shape}"
        print("   ✓ 小波域处理器测试通过")
        
        # 测试多模态处理器
        print("3. 测试多模态处理器...")
        multimodal_processor = MultiModalProcessor(use_original=True, use_frequency=True, use_wavelet=True)
        multimodal_features = multimodal_processor.process_image(test_image)
        
        expected_keys = ['original', 'frequency', 'wavelet']
        for key in expected_keys:
            assert key in multimodal_features, f"缺少特征: {key}"
            assert multimodal_features[key].shape == (3, 224, 224), f"{key}特征形状错误"
        print("   ✓ 多模态处理器测试通过")
        
        print("✓ 数据预处理模块测试全部通过\n")
        return True
        
    except Exception as e:
        print(f"✗ 数据预处理模块测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("=" * 50)
    print("测试模型创建")
    print("=" * 50)
    
    try:
        # 测试V4a频率域模型
        print("1. 测试V4a频率域模型...")
        sys.path.append('.')
        from single_classifier_trainer_v4a_frequency import SingleScreenRecaptureDetector
        
        model_v4a = SingleScreenRecaptureDetector(backbone='efficientnet-b4', num_classes=2)
        test_input = torch.randn(2, 3, 224, 224)
        output = model_v4a(test_input)
        assert output.shape == (2, 2), f"V4a模型输出形状错误: {output.shape}"
        print("   ✓ V4a频率域模型测试通过")
        
        # 测试V4b小波域模型
        print("2. 测试V4b小波域模型...")
        from single_classifier_trainer_v4b_wavelet import SingleScreenRecaptureDetector as SingleScreenRecaptureDetectorWavelet
        
        model_v4b = SingleScreenRecaptureDetectorWavelet(backbone='efficientnet-b4', num_classes=2)
        output = model_v4b(test_input)
        assert output.shape == (2, 2), f"V4b模型输出形状错误: {output.shape}"
        print("   ✓ V4b小波域模型测试通过")
        
        # 测试V4c融合模型
        print("3. 测试V4c融合模型...")
        from single_classifier_trainer_v4c_fusion import MultiModalScreenRecaptureDetector
        
        model_v4c = MultiModalScreenRecaptureDetector(backbone='efficientnet-b4', num_classes=2)
        output, attention_weights = model_v4c(test_input, test_input, test_input)
        assert output.shape == (2, 2), f"V4c模型输出形状错误: {output.shape}"
        assert attention_weights.shape == (2, 3), f"V4c注意力权重形状错误: {attention_weights.shape}"
        print("   ✓ V4c融合模型测试通过")
        
        print("✓ 模型创建测试全部通过\n")
        return True
        
    except Exception as e:
        print(f"✗ 模型创建测试失败: {e}")
        return False

def test_dataset_creation():
    """测试数据集创建"""
    print("=" * 50)
    print("测试数据集创建")
    print("=" * 50)
    
    try:
        # 创建临时测试数据
        temp_dir = tempfile.mkdtemp()
        train_dir = os.path.join(temp_dir, 'train')
        os.makedirs(os.path.join(train_dir, 'real'), exist_ok=True)
        os.makedirs(os.path.join(train_dir, 'fake'), exist_ok=True)
        
        # 创建测试图像
        for i in range(2):
            test_image = Image.new('RGB', (224, 224), color='red')
            test_image.save(os.path.join(train_dir, 'real', f'real_{i}.jpg'))
            test_image = Image.new('RGB', (224, 224), color='blue')
            test_image.save(os.path.join(train_dir, 'fake', f'fake_{i}.jpg'))
        
        # 测试V4a数据集
        print("1. 测试V4a频率域数据集...")
        from single_classifier_trainer_v4a_frequency import FrequencyDataset
        from data_preprocessing_v4 import FrequencyDomainProcessor
        from torchvision import transforms
        
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
        freq_processor = FrequencyDomainProcessor()
        dataset_v4a = FrequencyDataset(train_dir, freq_processor, transform)
        assert len(dataset_v4a) == 4, f"V4a数据集大小错误: {len(dataset_v4a)}"
        
        sample = dataset_v4a[0]
        assert len(sample) == 2, "V4a数据集样本格式错误"
        assert sample[0].shape == (3, 224, 224), f"V4a样本特征形状错误: {sample[0].shape}"
        print("   ✓ V4a频率域数据集测试通过")
        
        # 测试V4b数据集
        print("2. 测试V4b小波域数据集...")
        from single_classifier_trainer_v4b_wavelet import WaveletDataset
        from data_preprocessing_v4 import WaveletDomainProcessor
        
        wavelet_processor = WaveletDomainProcessor()
        dataset_v4b = WaveletDataset(train_dir, wavelet_processor, transform)
        assert len(dataset_v4b) == 4, f"V4b数据集大小错误: {len(dataset_v4b)}"
        
        sample = dataset_v4b[0]
        assert len(sample) == 2, "V4b数据集样本格式错误"
        assert sample[0].shape == (3, 224, 224), f"V4b样本特征形状错误: {sample[0].shape}"
        print("   ✓ V4b小波域数据集测试通过")
        
        # 测试V4c数据集
        print("3. 测试V4c融合数据集...")
        from single_classifier_trainer_v4c_fusion import MultiModalDataset
        from data_preprocessing_v4 import MultiModalProcessor
        
        multimodal_processor = MultiModalProcessor(use_original=True, use_frequency=True, use_wavelet=True)
        
        # 定义变换函数
        def combined_transform(features_dict):
            result = {}
            original_transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            domain_transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
            ])
            
            for key, features in features_dict.items():
                if key == 'original':
                    result[key] = original_transform(features)
                else:
                    result[key] = domain_transform(features)
            return result
        
        dataset_v4c = MultiModalDataset(train_dir, multimodal_processor, combined_transform)
        assert len(dataset_v4c) == 4, f"V4c数据集大小错误: {len(dataset_v4c)}"
        
        sample = dataset_v4c[0]
        assert len(sample) == 2, "V4c数据集样本格式错误"
        features, label = sample
        assert 'original' in features and 'frequency' in features and 'wavelet' in features, "V4c样本特征缺失"
        print("   ✓ V4c融合数据集测试通过")
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        
        print("✓ 数据集创建测试全部通过\n")
        return True
        
    except Exception as e:
        print(f"✗ 数据集创建测试失败: {e}")
        if 'temp_dir' in locals():
            shutil.rmtree(temp_dir)
        return False

def test_inference_scripts():
    """测试推理脚本"""
    print("=" * 50)
    print("测试推理脚本")
    print("=" * 50)
    
    try:
        # 测试推理脚本的导入
        print("1. 测试推理脚本导入...")
        
        # 测试V4a推理脚本
        from single_classifier_inference_v4a_frequency import load_model, get_transforms, preprocess_image
        print("   ✓ V4a推理脚本导入成功")
        
        # 测试V4b推理脚本
        from single_classifier_inference_v4b_wavelet import load_model as load_model_v4b
        print("   ✓ V4b推理脚本导入成功")
        
        # 测试V4c推理脚本
        from single_classifier_inference_v4c_fusion import load_model as load_model_v4c
        print("   ✓ V4c推理脚本导入成功")
        
        print("✓ 推理脚本测试通过\n")
        return True
        
    except Exception as e:
        print(f"✗ 推理脚本测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始V4版本实现测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("数据预处理模块", test_data_preprocessing()))
    test_results.append(("模型创建", test_model_creation()))
    test_results.append(("数据集创建", test_dataset_creation()))
    test_results.append(("推理脚本", test_inference_scripts()))
    
    # 汇总测试结果
    print("=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过! V4版本实现正确。")
        print("\n接下来可以:")
        print("1. 准备训练数据")
        print("2. 运行训练脚本:")
        print("   - bash run_single_classifier_training_v4a_frequency.sh")
        print("   - bash run_single_classifier_training_v4b_wavelet.sh")
        print("   - bash run_single_classifier_training_v4c_fusion.sh")
        print("3. 使用性能对比分析脚本比较结果")
        return True
    else:
        print(f"\n❌ {total - passed} 项测试失败，请检查实现。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
