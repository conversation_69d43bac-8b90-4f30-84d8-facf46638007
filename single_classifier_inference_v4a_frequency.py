#!/usr/bin/env python3
"""
单分类器屏幕翻拍检测推理脚本 V4a - 频率域版本
- 基于V1 seeded版本的推理流程
- 专门处理频率域输入数据
- 支持单张图像和批量推理
"""

import os
import torch
import torch.nn as nn
from torchvision import transforms
import numpy as np
from PIL import Image
import argparse
import json
import cv2
from tqdm import tqdm
import glob

from model.efficientnet import EfficientNet
from data_preprocessing_v4 import FrequencyDomainProcessor


class SingleScreenRecaptureDetector(nn.Module):
    """单分类器屏幕翻拍检测模型 - 频率域版本"""
    
    def __init__(self, backbone='efficientnet-b4', num_classes=2, drop_rate=0.2):
        super().__init__()
        
        # 主干网络 - EfficientNet
        self.backbone = EfficientNet.from_pretrained(
            backbone, 
            advprop=True,
            num_classes=num_classes,
            dropout_rate=drop_rate,
            include_top=False
        )
        
        # 获取backbone输出特征维度
        backbone_features = self.backbone._bn1.num_features  # EfficientNet-B4: 1792
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(drop_rate),
            nn.Linear(backbone_features, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(256, num_classes)
        )
    
    def extract_backbone_features(self, x):
        """提取主干网络特征"""
        # Stem
        x = self.backbone._swish(self.backbone._bn0(self.backbone._conv_stem(x)))
        
        # Blocks
        for idx, block in enumerate(self.backbone._blocks):
            drop_connect_rate = self.backbone._global_params.drop_connect_rate
            if drop_connect_rate:
                drop_connect_rate *= float(idx) / len(self.backbone._blocks)
            x = block(x, drop_connect_rate=drop_connect_rate)
        
        # Head
        x = self.backbone._swish(self.backbone._bn1(self.backbone._conv_head(x)))
        
        return x
    
    def forward(self, x):
        # 提取backbone特征
        features = self.extract_backbone_features(x)
        
        # 全局池化
        pooled_features = self.global_pool(features)
        pooled_features = pooled_features.view(pooled_features.size(0), -1)
        
        # 分类
        output = self.classifier(pooled_features)
        
        return output


def load_model(checkpoint_path, device):
    """加载训练好的模型"""
    print(f"Loading model from {checkpoint_path}")
    
    # 加载checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 创建模型
    model = SingleScreenRecaptureDetector(backbone='efficientnet-b4', num_classes=2)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f"Model loaded successfully!")
    print(f"Model type: {checkpoint.get('model_type', 'unknown')}")
    print(f"Best AUC: {checkpoint.get('best_auc', 'unknown')}")
    print(f"Training seed: {checkpoint.get('seed', 'unknown')}")
    
    return model


def get_transform(input_size=224):
    """获取推理时的数据变换"""
    return transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])


def preprocess_image(image_path, freq_processor, transform):
    """预处理单张图像"""
    try:
        # 加载图像
        image = Image.open(image_path).convert('RGB')
        
        # 转换为频率域特征
        freq_features = freq_processor.process_image(image)
        
        # 应用变换
        if transform:
            freq_features = transform(freq_features)
        
        # 添加batch维度
        freq_features = freq_features.unsqueeze(0)
        
        return freq_features
        
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return None


def predict_single_image(model, image_path, freq_processor, transform, device):
    """对单张图像进行预测"""
    # 预处理
    input_tensor = preprocess_image(image_path, freq_processor, transform)
    if input_tensor is None:
        return None
    
    input_tensor = input_tensor.to(device)
    
    # 推理
    with torch.no_grad():
        outputs = model(input_tensor)
        probs = torch.softmax(outputs, dim=1)
        confidence = torch.max(probs, dim=1)[0].item()
        predicted_class = torch.argmax(outputs, dim=1).item()
    
    # 结果
    result = {
        'image_path': image_path,
        'predicted_class': predicted_class,
        'predicted_label': 'Fake' if predicted_class == 1 else 'Real',
        'confidence': confidence,
        'real_prob': probs[0][0].item(),
        'fake_prob': probs[0][1].item()
    }
    
    return result


def predict_batch(model, image_paths, freq_processor, transform, device, batch_size=16):
    """批量预测"""
    results = []
    
    for i in tqdm(range(0, len(image_paths), batch_size), desc="Processing batches"):
        batch_paths = image_paths[i:i+batch_size]
        batch_tensors = []
        valid_paths = []
        
        # 预处理batch
        for path in batch_paths:
            tensor = preprocess_image(path, freq_processor, transform)
            if tensor is not None:
                batch_tensors.append(tensor)
                valid_paths.append(path)
        
        if not batch_tensors:
            continue
        
        # 合并为batch
        batch_input = torch.cat(batch_tensors, dim=0).to(device)
        
        # 推理
        with torch.no_grad():
            outputs = model(batch_input)
            probs = torch.softmax(outputs, dim=1)
            confidences = torch.max(probs, dim=1)[0]
            predicted_classes = torch.argmax(outputs, dim=1)
        
        # 收集结果
        for j, path in enumerate(valid_paths):
            result = {
                'image_path': path,
                'predicted_class': predicted_classes[j].item(),
                'predicted_label': 'Fake' if predicted_classes[j].item() == 1 else 'Real',
                'confidence': confidences[j].item(),
                'real_prob': probs[j][0].item(),
                'fake_prob': probs[j][1].item()
            }
            results.append(result)
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Single Classifier Screen Recapture Detector Inference V4a - Frequency Domain')
    parser.add_argument('--checkpoint', type=str, required=True, help='模型checkpoint路径')
    parser.add_argument('--input', type=str, required=True, help='输入图像路径或目录')
    parser.add_argument('--output', type=str, help='输出结果文件路径 (JSON格式)')
    parser.add_argument('--batch_size', type=int, default=16, help='批量推理的批次大小')
    parser.add_argument('--input_size', type=int, default=224, help='输入图像尺寸')
    
    args = parser.parse_args()
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # 加载模型
    model = load_model(args.checkpoint, device)
    
    # 初始化频率域处理器和变换
    freq_processor = FrequencyDomainProcessor(
        use_magnitude=True, 
        use_phase=True, 
        log_scale=True
    )
    transform = get_transform(args.input_size)
    
    # 确定输入类型
    if os.path.isfile(args.input):
        # 单张图像
        print(f"Processing single image: {args.input}")
        result = predict_single_image(model, args.input, freq_processor, transform, device)
        
        if result:
            print(f"\nPrediction Result:")
            print(f"Image: {result['image_path']}")
            print(f"Predicted: {result['predicted_label']}")
            print(f"Confidence: {result['confidence']:.4f}")
            print(f"Real Probability: {result['real_prob']:.4f}")
            print(f"Fake Probability: {result['fake_prob']:.4f}")
            
            results = [result]
        else:
            print("Failed to process the image.")
            return
            
    elif os.path.isdir(args.input):
        # 目录批量处理
        print(f"Processing directory: {args.input}")
        
        # 收集所有图像文件
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        image_paths = []
        for ext in image_extensions:
            image_paths.extend(glob.glob(os.path.join(args.input, '**', ext), recursive=True))
        
        print(f"Found {len(image_paths)} images")
        
        if not image_paths:
            print("No images found in the directory.")
            return
        
        # 批量预测
        results = predict_batch(model, image_paths, freq_processor, transform, device, args.batch_size)
        
        # 统计结果
        real_count = sum(1 for r in results if r['predicted_class'] == 0)
        fake_count = sum(1 for r in results if r['predicted_class'] == 1)
        
        print(f"\nBatch Processing Results:")
        print(f"Total processed: {len(results)}")
        print(f"Predicted Real: {real_count}")
        print(f"Predicted Fake: {fake_count}")
        
    else:
        print(f"Invalid input path: {args.input}")
        return
    
    # 保存结果
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\nResults saved to: {args.output}")
    
    print("\n单分类器模型 V4a - 频率域版本 推理完成!")
    print("模型特点:")
    print("- 使用FFT变换提取频率域特征")
    print("- 对屏幕翻拍的频率特征敏感")
    print("- 基于V1 seeded版本架构")


if __name__ == '__main__':
    main()
