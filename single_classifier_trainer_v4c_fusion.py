#!/usr/bin/env python3
"""
单分类器屏幕翻拍检测训练脚本 V4c - 多模态融合版本
- 基于V1 seeded版本
- 同时处理原图、频率域和小波域特征
- 使用注意力机制进行特征融合
- 保持其他训练参数与V1一致以便对比
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms
import numpy as np
from PIL import Image
import argparse
from tqdm import tqdm
import json
import cv2
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import random

from model.efficientnet import EfficientNet
from data_preprocessing_v4 import MultiModalProcessor


def set_random_seeds(seed=42):
    """设置所有随机种子确保可复现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"Random seeds set to {seed}")


class AttentionFusion(nn.Module):
    """注意力融合模块"""
    
    def __init__(self, feature_dim):
        super().__init__()
        self.feature_dim = feature_dim
        
        # 注意力权重计算
        self.attention = nn.Sequential(
            nn.Linear(feature_dim * 3, feature_dim),
            nn.ReLU(inplace=True),
            nn.Linear(feature_dim, 3),
            nn.Softmax(dim=1)
        )
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(feature_dim * 2, feature_dim)
        )
    
    def forward(self, original_features, freq_features, wavelet_features):
        """
        Args:
            original_features: 原图特征 (B, feature_dim)
            freq_features: 频率域特征 (B, feature_dim)
            wavelet_features: 小波域特征 (B, feature_dim)
        Returns:
            fused_features: 融合后的特征 (B, feature_dim)
        """
        # 拼接所有特征
        concat_features = torch.cat([original_features, freq_features, wavelet_features], dim=1)
        
        # 计算注意力权重
        attention_weights = self.attention(concat_features)  # (B, 3)
        
        # 加权融合
        weighted_original = original_features * attention_weights[:, 0:1]
        weighted_freq = freq_features * attention_weights[:, 1:2]
        weighted_wavelet = wavelet_features * attention_weights[:, 2:3]
        
        # 特征融合
        fused_input = torch.cat([weighted_original, weighted_freq, weighted_wavelet], dim=1)
        fused_features = self.fusion(fused_input)
        
        return fused_features, attention_weights


class MultiModalScreenRecaptureDetector(nn.Module):
    """多模态单分类器屏幕翻拍检测模型"""
    
    def __init__(self, backbone='efficientnet-b4', num_classes=2, drop_rate=0.2):
        super().__init__()
        
        # 三个独立的主干网络用于不同模态
        self.original_backbone = EfficientNet.from_pretrained(
            backbone, advprop=True, num_classes=num_classes,
            dropout_rate=drop_rate, include_top=False
        )
        
        self.freq_backbone = EfficientNet.from_pretrained(
            backbone, advprop=True, num_classes=num_classes,
            dropout_rate=drop_rate, include_top=False
        )
        
        self.wavelet_backbone = EfficientNet.from_pretrained(
            backbone, advprop=True, num_classes=num_classes,
            dropout_rate=drop_rate, include_top=False
        )
        
        # 获取backbone输出特征维度
        backbone_features = self.original_backbone._bn1.num_features  # EfficientNet-B4: 1792
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 注意力融合模块
        self.attention_fusion = AttentionFusion(backbone_features)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(drop_rate),
            nn.Linear(backbone_features, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(256, num_classes)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化分类头权重"""
        for m in self.classifier.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def extract_backbone_features(self, x, backbone):
        """提取主干网络特征"""
        # Stem
        x = backbone._swish(backbone._bn0(backbone._conv_stem(x)))
        
        # Blocks
        for idx, block in enumerate(backbone._blocks):
            drop_connect_rate = backbone._global_params.drop_connect_rate
            if drop_connect_rate:
                drop_connect_rate *= float(idx) / len(backbone._blocks)
            x = block(x, drop_connect_rate=drop_connect_rate)
        
        # Head
        x = backbone._swish(backbone._bn1(backbone._conv_head(x)))
        
        return x
    
    def forward(self, original_x, freq_x, wavelet_x):
        """
        Args:
            original_x: 原图输入 (B, 3, H, W)
            freq_x: 频率域输入 (B, 3, H, W)
            wavelet_x: 小波域输入 (B, 3, H, W)
        """
        # 提取各模态特征
        original_features = self.extract_backbone_features(original_x, self.original_backbone)
        freq_features = self.extract_backbone_features(freq_x, self.freq_backbone)
        wavelet_features = self.extract_backbone_features(wavelet_x, self.wavelet_backbone)
        
        # 全局池化
        original_pooled = self.global_pool(original_features).view(original_features.size(0), -1)
        freq_pooled = self.global_pool(freq_features).view(freq_features.size(0), -1)
        wavelet_pooled = self.global_pool(wavelet_features).view(wavelet_features.size(0), -1)
        
        # 注意力融合
        fused_features, attention_weights = self.attention_fusion(
            original_pooled, freq_pooled, wavelet_pooled
        )
        
        # 分类
        output = self.classifier(fused_features)
        
        return output, attention_weights


class MultiModalDataset(Dataset):
    """多模态数据集"""
    
    def __init__(self, data_dir, transform=None, split='train'):
        self.data_dir = data_dir
        self.transform = transform
        self.split = split
        
        # 初始化多模态处理器
        self.multimodal_processor = MultiModalProcessor(
            use_original=True,
            use_frequency=True,
            use_wavelet=True
        )
        
        self.samples = []
        
        # 真实拍摄 (标签0)
        real_dir = os.path.join(data_dir, 'real')
        if os.path.exists(real_dir):
            for img_name in os.listdir(real_dir):
                if img_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                    self.samples.append((os.path.join(real_dir, img_name), 0))
        
        # 屏幕翻拍 (标签1)
        fake_dir = os.path.join(data_dir, 'fake')
        if os.path.exists(fake_dir):
            for img_name in os.listdir(fake_dir):
                if img_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                    self.samples.append((os.path.join(fake_dir, img_name), 1))
        
        print(f"{split} dataset: {len(self.samples)} samples")
        
        # 统计标签分布
        labels = [sample[1] for sample in self.samples]
        print(f"Real: {labels.count(0)}, Fake: {labels.count(1)}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        
        # 加载图像
        try:
            image = Image.open(img_path).convert('RGB')
        except Exception as e:
            print(f"Error loading {img_path}: {e}")
            image = Image.new('RGB', (224, 224), (128, 128, 128))
        
        # 生成多模态特征
        multimodal_features = self.multimodal_processor.process_image(image)
        
        # 应用数据变换（如果有）
        if self.transform:
            for key in multimodal_features:
                multimodal_features[key] = self.transform(multimodal_features[key])
        
        return multimodal_features, label


def get_transforms(input_size=224):
    """获取数据变换"""
    
    # 原图使用标准的ImageNet归一化
    original_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 频率域和小波域使用不同的归一化
    domain_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    def combined_transform(features_dict):
        result = {}
        for key, features in features_dict.items():
            if key == 'original':
                result[key] = original_transform(features)
            else:
                result[key] = domain_transform(features)
        return result
    
    return combined_transform, combined_transform  # 训练和验证使用相同变换


def train_epoch(model, dataloader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')

    for batch_idx, (multimodal_features, labels) in enumerate(pbar):
        # 将数据移到设备
        original_x = multimodal_features['original'].to(device)
        freq_x = multimodal_features['frequency'].to(device)
        wavelet_x = multimodal_features['wavelet'].to(device)
        labels = labels.to(device)

        optimizer.zero_grad()

        # 前向传播
        outputs, attention_weights = model(original_x, freq_x, wavelet_x)
        loss = criterion(outputs, labels)

        # 反向传播
        loss.backward()
        optimizer.step()

        # 统计
        running_loss += loss.item()
        _, predicted = torch.max(outputs, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()

        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{running_loss/(batch_idx+1):.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })

        # 定期清理GPU内存
        if batch_idx % 100 == 0:
            torch.cuda.empty_cache()

    return {
        'loss': running_loss / len(dataloader),
        'accuracy': 100. * correct / total
    }


def validate_epoch(model, dataloader, criterion, device):
    """验证一个epoch"""
    model.eval()
    running_loss = 0.0
    all_labels = []
    all_preds = []
    all_probs = []
    all_attention_weights = []

    with torch.no_grad():
        for multimodal_features, labels in tqdm(dataloader, desc='Validating'):
            # 将数据移到设备
            original_x = multimodal_features['original'].to(device)
            freq_x = multimodal_features['frequency'].to(device)
            wavelet_x = multimodal_features['wavelet'].to(device)
            labels = labels.to(device)

            outputs, attention_weights = model(original_x, freq_x, wavelet_x)
            loss = criterion(outputs, labels)

            running_loss += loss.item()

            # 收集预测结果
            probs = torch.softmax(outputs, dim=1)
            _, preds = torch.max(outputs, 1)

            all_labels.extend(labels.cpu().numpy())
            all_preds.extend(preds.cpu().numpy())
            all_probs.extend(probs[:, 1].cpu().numpy())  # 翻拍概率
            all_attention_weights.append(attention_weights.cpu().numpy())

    # 计算指标
    val_loss = running_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)

    try:
        auc = roc_auc_score(all_labels, all_probs)
    except:
        auc = 0.0

    # 分类报告
    class_report = classification_report(all_labels, all_preds,
                                       target_names=['Real', 'Fake'],
                                       output_dict=True)

    # 分析注意力权重
    all_attention_weights = np.concatenate(all_attention_weights, axis=0)
    avg_attention = np.mean(all_attention_weights, axis=0)

    return {
        'loss': val_loss,
        'accuracy': accuracy * 100,
        'auc': auc,
        'classification_report': class_report,
        'attention_weights': {
            'original': float(avg_attention[0]),
            'frequency': float(avg_attention[1]),
            'wavelet': float(avg_attention[2])
        }
    }


def main():
    parser = argparse.ArgumentParser(description='Single Classifier Screen Recapture Detector Training V4c - Multi-Modal Fusion')
    parser.add_argument('--train_dir', type=str, required=True, help='训练数据目录路径')
    parser.add_argument('--val_dir', type=str, required=True, help='验证数据目录路径')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小 (融合模型需要更多内存)')
    parser.add_argument('--epochs', type=int, default=30, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--save_dir', type=str, default='./single_classifier_checkpoints_v4c_fusion', help='模型保存目录')
    parser.add_argument('--input_size', type=int, default=224, help='输入图像尺寸')
    parser.add_argument('--backbone', type=str, default='efficientnet-b4', help='主干网络')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    # 设置随机种子
    set_random_seeds(args.seed)

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # 数据变换
    train_transform, val_transform = get_transforms(args.input_size)

    # 数据集
    train_dataset = MultiModalDataset(args.train_dir, train_transform, 'train')
    val_dataset = MultiModalDataset(args.val_dir, val_transform, 'val')

    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)

    # 模型
    model = MultiModalScreenRecaptureDetector(backbone=args.backbone, num_classes=2)
    model = model.to(device)

    print(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")

    # 计算类别权重处理数据不平衡
    train_real = sum(1 for _, label in train_dataset.samples if label == 0)
    train_fake = sum(1 for _, label in train_dataset.samples if label == 1)
    total_samples = train_real + train_fake

    print(f"Dataset statistics: Real={train_real}, Fake={train_fake}, Total={total_samples}")

    if train_real == 0 or train_fake == 0:
        print("Error: Missing one class in training data!")
        print("Please check data directory structure:")
        print("- Expected: train_dir/real/ and train_dir/fake/")
        return

    real_weight = total_samples / (2 * train_real)
    fake_weight = total_samples / (2 * train_fake)
    class_weights = torch.tensor([real_weight, fake_weight]).to(device)

    print(f"Class weights: Real={real_weight:.3f}, Fake={fake_weight:.3f}")

    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)

    # 训练历史
    history = {'train': [], 'val': []}
    best_auc = 0.0

    print("\n开始训练单分类器屏幕翻拍检测模型 V4c - 多模态融合版本...")
    print("模型特点:")
    print("1. 基于V1 seeded版本架构")
    print("2. 融合原图、频率域和小波域特征")
    print("3. 使用注意力机制进行特征融合")
    print("4. 三个独立的EfficientNet-B4 backbone")
    print("5. 类别权重平衡处理数据不平衡")
    print("6. 随机种子确保可复现性")

    # 训练循环
    for epoch in range(args.epochs):
        print(f'\nEpoch {epoch+1}/{args.epochs}')
        print('-' * 50)

        # 训练
        train_metrics = train_epoch(model, train_loader, criterion, optimizer, device, epoch+1)

        # 验证
        val_metrics = validate_epoch(model, val_loader, criterion, device)

        # 学习率调度
        scheduler.step()

        # 记录历史
        history['train'].append(train_metrics)
        history['val'].append(val_metrics)

        # 打印结果
        print(f'Train Loss: {train_metrics["loss"]:.4f}, Acc: {train_metrics["accuracy"]:.2f}%')
        print(f'Val Loss: {val_metrics["loss"]:.4f}, Acc: {val_metrics["accuracy"]:.2f}%, AUC: {val_metrics["auc"]:.4f}')

        # 打印注意力权重
        if 'attention_weights' in val_metrics:
            att_weights = val_metrics['attention_weights']
            print(f'Attention Weights - Original: {att_weights["original"]:.3f}, '
                  f'Frequency: {att_weights["frequency"]:.3f}, '
                  f'Wavelet: {att_weights["wavelet"]:.3f}')

        # 打印分类报告
        if 'classification_report' in val_metrics:
            report = val_metrics['classification_report']
            print(f'Real Precision: {report["Real"]["precision"]:.3f}, Recall: {report["Real"]["recall"]:.3f}')
            print(f'Fake Precision: {report["Fake"]["precision"]:.3f}, Recall: {report["Fake"]["recall"]:.3f}')

        # 保存最佳模型
        if val_metrics['auc'] > best_auc:
            best_auc = val_metrics['auc']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_auc': best_auc,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics,
                'class_weights': class_weights,
                'seed': args.seed,
                'model_type': 'v4c_fusion'
            }, os.path.join(args.save_dir, 'best_single_classifier_v4c_fusion.pth'))
            print(f'New best model saved! AUC: {best_auc:.4f}')

        # 保存最新模型
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'best_auc': best_auc,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'class_weights': class_weights,
            'seed': args.seed,
            'model_type': 'v4c_fusion'
        }, os.path.join(args.save_dir, 'latest_single_classifier_v4c_fusion.pth'))

    # 保存训练历史
    with open(os.path.join(args.save_dir, 'single_classifier_history_v4c_fusion.json'), 'w') as f:
        # 转换numpy类型为Python原生类型
        history_serializable = {}
        for key, value in history.items():
            history_serializable[key] = []
            for epoch_data in value:
                epoch_serializable = {}
                for k, v in epoch_data.items():
                    if isinstance(v, (np.integer, np.floating)):
                        epoch_serializable[k] = float(v)
                    elif isinstance(v, dict):
                        epoch_serializable[k] = v  # classification_report和attention_weights已经是可序列化的
                    else:
                        epoch_serializable[k] = v
                history_serializable[key].append(epoch_serializable)

        json.dump(history_serializable, f, indent=2)

    print(f'\n训练完成! 最佳AUC: {best_auc:.4f}')
    print("\n单分类器模型 V4c - 多模态融合版本 特点:")
    print("- 融合原图、频率域和小波域的互补信息")
    print("- 注意力机制自动学习各模态的重要性")
    print("- 更全面的特征表示能力")
    print("- 保持与V1相同的训练策略")


if __name__ == '__main__':
    main()
