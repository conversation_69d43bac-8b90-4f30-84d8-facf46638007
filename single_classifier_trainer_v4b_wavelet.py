#!/usr/bin/env python3
"""
单分类器屏幕翻拍检测训练脚本 V4b - 小波域版本
- 基于V1 seeded版本
- 专门处理小波域输入数据（小波分解后的低频和高频特征）
- 保持其他训练参数与V1一致以便对比
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms
import numpy as np
from PIL import Image
import argparse
from tqdm import tqdm
import json
import cv2
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import random

from model.efficientnet import EfficientNet
from data_preprocessing_v4 import WaveletDomainProcessor


def set_random_seeds(seed=42):
    """设置所有随机种子确保可复现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"Random seeds set to {seed}")


class SingleScreenRecaptureDetector(nn.Module):
    """单分类器屏幕翻拍检测模型 - 小波域版本"""
    
    def __init__(self, backbone='efficientnet-b4', num_classes=2, drop_rate=0.2):
        super().__init__()
        
        # 主干网络 - EfficientNet
        self.backbone = EfficientNet.from_pretrained(
            backbone, 
            advprop=True,
            num_classes=num_classes,
            dropout_rate=drop_rate,
            include_top=False
        )
        
        # 获取backbone输出特征维度
        backbone_features = self.backbone._bn1.num_features  # EfficientNet-B4: 1792
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(drop_rate),
            nn.Linear(backbone_features, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(drop_rate),
            nn.Linear(256, num_classes)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化分类头权重"""
        for m in self.classifier.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 提取backbone特征
        features = self.extract_backbone_features(x)
        
        # 全局池化
        pooled_features = self.global_pool(features)
        pooled_features = pooled_features.view(pooled_features.size(0), -1)
        
        # 分类
        output = self.classifier(pooled_features)
        
        return output
    
    def extract_backbone_features(self, x):
        """提取主干网络特征"""
        # Stem
        x = self.backbone._swish(self.backbone._bn0(self.backbone._conv_stem(x)))
        
        # Blocks
        for idx, block in enumerate(self.backbone._blocks):
            drop_connect_rate = self.backbone._global_params.drop_connect_rate
            if drop_connect_rate:
                drop_connect_rate *= float(idx) / len(self.backbone._blocks)
            x = block(x, drop_connect_rate=drop_connect_rate)
        
        # Head
        x = self.backbone._swish(self.backbone._bn1(self.backbone._conv_head(x)))
        
        return x


class WaveletDataset(Dataset):
    """小波域数据集"""
    
    def __init__(self, data_dir, transform=None, split='train'):
        self.data_dir = data_dir
        self.transform = transform
        self.split = split
        
        # 初始化小波域处理器
        self.wavelet_processor = WaveletDomainProcessor(
            wavelet='db4',  # Daubechies 4小波
            levels=3,       # 3层分解
            mode='symmetric'
        )
        
        self.samples = []
        
        # 真实拍摄 (标签0)
        real_dir = os.path.join(data_dir, 'real')
        if os.path.exists(real_dir):
            for img_name in os.listdir(real_dir):
                if img_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                    self.samples.append((os.path.join(real_dir, img_name), 0))
        
        # 屏幕翻拍 (标签1)
        fake_dir = os.path.join(data_dir, 'fake')
        if os.path.exists(fake_dir):
            for img_name in os.listdir(fake_dir):
                if img_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                    self.samples.append((os.path.join(fake_dir, img_name), 1))
        
        print(f"{split} dataset: {len(self.samples)} samples")
        
        # 统计标签分布
        labels = [sample[1] for sample in self.samples]
        print(f"Real: {labels.count(0)}, Fake: {labels.count(1)}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        
        # 加载图像
        try:
            image = Image.open(img_path).convert('RGB')
        except Exception as e:
            print(f"Error loading {img_path}: {e}")
            image = Image.new('RGB', (224, 224), (128, 128, 128))
        
        # 转换为小波域特征
        wavelet_features = self.wavelet_processor.process_image(image)
        
        # 应用数据变换（如果有）
        if self.transform:
            # 注意：这里的transform应该适用于小波域数据
            wavelet_features = self.transform(wavelet_features)
        
        return wavelet_features, label


def get_transforms(input_size=224):
    """获取数据变换 - 适用于小波域数据"""
    
    # 小波域数据的变换（主要是归一化和可能的增强）
    train_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        # 小波域数据已经在预处理中归一化了，这里可以添加其他变换
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])  # 调整到[-1,1]
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    return train_transform, val_transform


def train_epoch(model, dataloader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')
    
    for batch_idx, (images, labels) in enumerate(pbar):
        images = images.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(images)
        loss = criterion(outputs, labels)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        running_loss += loss.item()
        _, predicted = torch.max(outputs, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{running_loss/(batch_idx+1):.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
        
        # 定期清理GPU内存
        if batch_idx % 100 == 0:
            torch.cuda.empty_cache()
    
    return {
        'loss': running_loss / len(dataloader),
        'accuracy': 100. * correct / total
    }


def validate_epoch(model, dataloader, criterion, device):
    """验证一个epoch"""
    model.eval()
    running_loss = 0.0
    all_labels = []
    all_preds = []
    all_probs = []
    
    with torch.no_grad():
        for images, labels in tqdm(dataloader, desc='Validating'):
            images = images.to(device)
            labels = labels.to(device)
            
            outputs = model(images)
            loss = criterion(outputs, labels)
            
            running_loss += loss.item()
            
            # 收集预测结果
            probs = torch.softmax(outputs, dim=1)
            _, preds = torch.max(outputs, 1)
            
            all_labels.extend(labels.cpu().numpy())
            all_preds.extend(preds.cpu().numpy())
            all_probs.extend(probs[:, 1].cpu().numpy())  # 翻拍概率
    
    # 计算指标
    val_loss = running_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    try:
        auc = roc_auc_score(all_labels, all_probs)
    except:
        auc = 0.0
    
    # 分类报告
    class_report = classification_report(all_labels, all_preds, 
                                       target_names=['Real', 'Fake'], 
                                       output_dict=True)
    
    return {
        'loss': val_loss,
        'accuracy': accuracy * 100,
        'auc': auc,
        'classification_report': class_report
    }


def main():
    parser = argparse.ArgumentParser(description='Single Classifier Screen Recapture Detector Training V4b - Wavelet Domain')
    parser.add_argument('--train_dir', type=str, required=True, help='训练数据目录路径')
    parser.add_argument('--val_dir', type=str, required=True, help='验证数据目录路径')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--epochs', type=int, default=30, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--save_dir', type=str, default='./single_classifier_checkpoints_v4b_wavelet', help='模型保存目录')
    parser.add_argument('--input_size', type=int, default=224, help='输入图像尺寸')
    parser.add_argument('--backbone', type=str, default='efficientnet-b4', help='主干网络')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    # 设置随机种子
    set_random_seeds(args.seed)

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # 数据变换
    train_transform, val_transform = get_transforms(args.input_size)

    # 数据集
    train_dataset = WaveletDataset(args.train_dir, train_transform, 'train')
    val_dataset = WaveletDataset(args.val_dir, val_transform, 'val')

    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)

    # 模型
    model = SingleScreenRecaptureDetector(backbone=args.backbone, num_classes=2)
    model = model.to(device)

    print(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")

    # 计算类别权重处理数据不平衡
    train_real = sum(1 for _, label in train_dataset.samples if label == 0)
    train_fake = sum(1 for _, label in train_dataset.samples if label == 1)
    total_samples = train_real + train_fake

    print(f"Dataset statistics: Real={train_real}, Fake={train_fake}, Total={total_samples}")

    if train_real == 0 or train_fake == 0:
        print("Error: Missing one class in training data!")
        print("Please check data directory structure:")
        print("- Expected: train_dir/real/ and train_dir/fake/")
        return

    real_weight = total_samples / (2 * train_real)
    fake_weight = total_samples / (2 * train_fake)
    class_weights = torch.tensor([real_weight, fake_weight]).to(device)

    print(f"Class weights: Real={real_weight:.3f}, Fake={fake_weight:.3f}")

    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)

    # 训练历史
    history = {'train': [], 'val': []}
    best_auc = 0.0

    print("\n开始训练单分类器屏幕翻拍检测模型 V4b - 小波域版本...")
    print("模型特点:")
    print("1. 基于V1 seeded版本架构")
    print("2. 使用小波域特征（多尺度小波分解）")
    print("3. EfficientNet-B4作为backbone")
    print("4. 类别权重平衡处理数据不平衡")
    print("5. 随机种子确保可复现性")

    # 训练循环
    for epoch in range(args.epochs):
        print(f'\nEpoch {epoch+1}/{args.epochs}')
        print('-' * 50)

        # 训练
        train_metrics = train_epoch(model, train_loader, criterion, optimizer, device, epoch+1)

        # 验证
        val_metrics = validate_epoch(model, val_loader, criterion, device)

        # 学习率调度
        scheduler.step()

        # 记录历史
        history['train'].append(train_metrics)
        history['val'].append(val_metrics)

        # 打印结果
        print(f'Train Loss: {train_metrics["loss"]:.4f}, Acc: {train_metrics["accuracy"]:.2f}%')
        print(f'Val Loss: {val_metrics["loss"]:.4f}, Acc: {val_metrics["accuracy"]:.2f}%, AUC: {val_metrics["auc"]:.4f}')

        # 打印分类报告
        if 'classification_report' in val_metrics:
            report = val_metrics['classification_report']
            print(f'Real Precision: {report["Real"]["precision"]:.3f}, Recall: {report["Real"]["recall"]:.3f}')
            print(f'Fake Precision: {report["Fake"]["precision"]:.3f}, Recall: {report["Fake"]["recall"]:.3f}')

        # 保存最佳模型
        if val_metrics['auc'] > best_auc:
            best_auc = val_metrics['auc']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_auc': best_auc,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics,
                'class_weights': class_weights,
                'seed': args.seed,
                'model_type': 'v4b_wavelet'
            }, os.path.join(args.save_dir, 'best_single_classifier_v4b_wavelet.pth'))
            print(f'New best model saved! AUC: {best_auc:.4f}')

        # 保存最新模型
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'best_auc': best_auc,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'class_weights': class_weights,
            'seed': args.seed,
            'model_type': 'v4b_wavelet'
        }, os.path.join(args.save_dir, 'latest_single_classifier_v4b_wavelet.pth'))

    # 保存训练历史
    with open(os.path.join(args.save_dir, 'single_classifier_history_v4b_wavelet.json'), 'w') as f:
        # 转换numpy类型为Python原生类型
        history_serializable = {}
        for key, value in history.items():
            history_serializable[key] = []
            for epoch_data in value:
                epoch_serializable = {}
                for k, v in epoch_data.items():
                    if isinstance(v, (np.integer, np.floating)):
                        epoch_serializable[k] = float(v)
                    elif isinstance(v, dict):
                        epoch_serializable[k] = v  # classification_report已经是可序列化的
                    else:
                        epoch_serializable[k] = v
                history_serializable[key].append(epoch_serializable)

        json.dump(history_serializable, f, indent=2)

    print(f'\n训练完成! 最佳AUC: {best_auc:.4f}')
    print("\n单分类器模型 V4b - 小波域版本 特点:")
    print("- 使用小波变换提取多尺度特征")
    print("- 低频和高频分量的组合特征")
    print("- 对图像纹理和边缘信息更敏感")
    print("- 保持与V1相同的训练策略")


if __name__ == '__main__':
    main()
